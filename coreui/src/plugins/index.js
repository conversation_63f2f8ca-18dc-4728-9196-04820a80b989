import DialogPlugin from './dialog-plugin';
import MapDialogPlugin from './map-dialog-plugin';
import Vue from "vue";
import "./echarts";
import VueGoogleMapsPlugin from "./vue-google-maps";

import {registerLicense} from '@syncfusion/ej2-base';
import {LicenseManager} from "ag-grid-enterprise";

import {Sketch} from "vue-color";

import {Datetime} from "vue-datetime";
import "vue-datetime/dist/vue-datetime.css";

import "core-js/stable";

import VueHtmlToPaper from "vue-html-to-paper";


import CoreuiVue from "@coreui/vue";
import { renewAgGridLicense, renewLicense } from '../utils/agGridLicenseGenerator';



Vue.component("datetime", Datetime);
Vue.component("sketch-picker", Sketch);


LicenseManager.setLicenseKey(renewAgGridLicense());
registerLicense(renewLicense())


Vue.use(CoreuiVue);
Vue.use(VueGoogleMapsPlugin);
Vue.use(DialogPlugin);
Vue.use(MapDialogPlugin);
Vue.use(Datetime);



const options = {
  name: "_blank",
  specs: ["fullscreen=yes", "titlebar=yes", "scrollbars=yes"],
  styles: [
    "https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css",
    "https://unpkg.com/kidlat-css/css/kidlat.css",
    location.origin + '/' + 'printable.css'
  ],
  timeout: 1000, // default timeout before the print window appears
  autoClose: true, // if false, the window will not close after printing
  windowTitle: window.document.title // override the window title
};

Vue.use(VueHtmlToPaper, options);
