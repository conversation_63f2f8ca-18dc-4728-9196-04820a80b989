
import ProgressBar from "../components/common/ProgressBar.vue";

const DialogPlugin = {
  install(Vue, options = {}) {
    const DialogConstructor = Vue.extend(ProgressBar);

    let dialogInstance = null;

    Vue.prototype.$dialog = {
      open(title, message, options = {}) {
        if (!dialogInstance) {
          dialogInstance = new DialogConstructor();
          dialogInstance.$mount();
          document.body.appendChild(dialogInstance.$el);
        }

        return dialogInstance.open(title, message, options);
      }
    };

    Vue.prototype.$globalDialog = Vue.prototype.$dialog;
  }
};

export default DialogPlugin;